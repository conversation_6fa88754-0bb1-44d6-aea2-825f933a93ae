#include "pch.h"
#include "MemoryReader.h"
#include "Map2.h"

#ifndef EXCEPTION_EXECUTE_HANDLER
#define EXCEPTION_EXECUTE_HANDLER 1
#endif
#include <iostream>
#include <sstream>
#include <vector>
#include <random>
//#include <iostream>


// Global variables
Config g_Config;
std::map<int, std::thread> g_BotThreads;
std::map<int, bool> g_BotRunning;
std::map<int, bool> g_KeyStates;
bool g_bRunning = true;
HANDLE g_hKeyMonitorThread = nullptr;
HWND g_gameWindow = NULL;
typedef std2::map<int, TargetInfo> t_TargetInfoMap;
// Parse skills from configuration string
std::vector<int> skillIDs;



void ShowConsole() {
    //'freopen': This function or variable may be unsafe. Consider using freopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
    
    //show console window
	AllocConsole();
	freopen("CONOUT$", "w", stdout); // Redirect stdout to console
	freopen("CONOUT$", "w", stderr); // Redirect stderr to console
	//SetConsoleTitle("Test");
	std::cout << "Console initialized." << std::endl;
}

void LoadConfig()
{
    std::ifstream file("data/Heth.cfg");
    if (!file.is_open())
    {
        // Create default config file if it doesn't exist
        SaveConfig();
        return;
    }

    std::string line;
    while (std::getline(file, line))
    {
        // Skip empty lines and comments
        if (line.empty() || line[0] == '#' || line[0] == ';')
            continue;

        // Find the equals sign
        size_t pos = line.find('=');
        if (pos == std::string::npos)
            continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        // Trim whitespace
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);

        // Parse configuration values
        if (key == "AutoHeal")
            g_Config.AutoHeal = (value == "True" || value == "true" || value == "1");
        else if (key == "AutoMana")
            g_Config.AutoMana = (value == "True" || value == "true" || value == "1");
        else if (key == "AutoSkill")
            g_Config.AutoSkill = (value == "True" || value == "true" || value == "1");
        else if (key == "AutoBehead")
            g_Config.AutoBehead = (value == "True" || value == "true" || value == "1");
        else if (key == "ManaKey")
            g_Config.ManaKey = std::stoi(value);
        else if (key == "HealthKey")
            g_Config.HealthKey = std::stoi(value);
        else if (key == "Skills")
            g_Config.Skills = value;
    }


    // Split the comma-separated skills string
    std::stringstream ss(g_Config.Skills);
    std::string skillStr;
    while (std::getline(ss, skillStr, ',')) {
        // Trim whitespace
        skillStr.erase(0, skillStr.find_first_not_of(" \t"));
        skillStr.erase(skillStr.find_last_not_of(" \t") + 1);

        if (!skillStr.empty()) {
            try {
                int skillID = std::stoi(skillStr);
                skillIDs.push_back(skillID);
            }
            catch (...) {
                // Skip invalid skill IDs
            }
        }
    }

    file.close();
	Engine::KGameSys::AddInfoMessage("Configuration loaded successfully", 0x00FF00, 0);
}

void SaveConfig()
{
    std::ofstream file("data/Heth.cfg");
    if (!file.is_open())
        return;

    file << "# Heth Bot Configuration File\n";
    file << "# Boolean values: True/False or 1/0\n";
    file << "# Key values: Virtual key codes (1-9 for number keys)\n";
    file << "# Skills: Comma-separated list of skill IDs (check SkillIDs.txt)\n\n";
    file << "# F8 To start AutoAttack bot\n";
    file << "# F9 To start AutoHeal or Mana bot\n\n";

    file << "AutoHeal=" << (g_Config.AutoHeal ? "True" : "False") << "\n";
    file << "AutoMana=" << (g_Config.AutoMana ? "True" : "False") << "\n";
    file << "AutoSkill=" << (g_Config.AutoSkill ? "True" : "False") << "\n";
    file << "AutoBehead=" << (g_Config.AutoBehead ? "True" : "False") << "\n";
    file << "ManaKey=" << g_Config.ManaKey << "\n";
    file << "HealthKey=" << g_Config.HealthKey << "\n";
    file << "Skills=" << g_Config.Skills << "\n";

    file.close();
}


TargetInfo GetNearestTarget(bool chk_isAlive) {
    t_TargetInfoMap::iterator it;
    TargetInfo nearestTarget; // Default constructor creates invalid target

    t_TargetInfoMap* myMap = reinterpret_cast<t_TargetInfoMap*>((void*)Engine::EntityIDs);

    if (myMap->size() == 0) {
        return nearestTarget;
    }

    PlayerInfo player;
    if (!player.IsValid()) {
        return nearestTarget;
    }

    float nearestDistance = FLT_MAX;
    bool foundValidTarget = false;

    printf(("[AB] Scanning " + std::to_string(myMap->size()) + " targets...").c_str());
    for (it = myMap->begin(); it != myMap->end(); ++it)
    {
        const int key = it->first;
        const TargetInfo& target = it->second;

        if (target.IsValid() && target.IsMonster()) {
			if (chk_isAlive && !target.IsAlive()) {
				continue; // Skip dead targets if chk_isAlive is true
			}
            float distance = player.GetDistanceFromTarget(target);

            /*printf("Target ID: %u, Distance: %.2f, HP: %u/%u (%.1f%%)\n",
                   target.GetTargetID(), distance, target.GetCurrentHP(),
                   target.GetMaxHP(), target.GetHPPercentage());*/

            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestTarget = target;
                foundValidTarget = true;
            }
        }
    }

    if (foundValidTarget) {
        //Engine::KGameSys::AddInfoMessage(("[AB] Nearest target found at distance: " + std::to_string(nearestDistance)).c_str(), 0x00FF00, 0);
        printf("=== Nearest Target ===\n");
        printf("Target ID: %u\n", nearestTarget.GetTargetID());
        printf("Position: (%d, %d)\n", nearestTarget.GetPosX(), nearestTarget.GetPosY());
        printf("Distance: %.2f\n", nearestDistance);
        printf("HP: %u/%u (%.1f%%)\n", nearestTarget.GetCurrentHP(), nearestTarget.GetMaxHP(), nearestTarget.GetHPPercentage());
        printf("======================\n");
    } else {
        Engine::KGameSys::AddInfoMessage("[AB] No valid alive monsters found", 0xFFFF00, 0);
    }

    return nearestTarget;
}

void DisplayPlayerInfo() {
    PlayerInfo player;

    printf("=== Player Info ===\n");

    if (player.IsValid()) {
        printf("Name: %s\n", player.GetName());
        printf("Position X: %d\n", player.GetPosX());
        printf("Position Y: %d\n", player.GetPosY());
        printf("Current HP: %d\n", player.GetCurrentHP());
        printf("Max HP: %d\n", player.GetMaxHP());
        printf("HP Percentage: %.1f%%\n", player.GetHPPercentage());
        printf("Current Mana: %d\n", player.GetCurrentMana());
        printf("Max Mana: %d\n", player.GetMaxMana());
        printf("Mana Percentage: %.1f%%\n", player.GetManaPercentage());
        printf("Is Alive: %s\n", player.IsAlive() ? "Yes" : "No");
    }
    else {
        printf("Player data not available (invalid master pointer)\n");
    }

    printf("===================\n\n");
}

HWND g_gameWindow = NULL;

// Function to find and cache the game window
void FindGameWindow() {
    if (g_gameWindow && IsWindow(g_gameWindow)) {
        return; // Already found and still valid
    }

    DWORD currentProcessId = GetCurrentProcessId();
    g_gameWindow = NULL;

    // Enumerate windows to find the main game window
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        DWORD processId;
        GetWindowThreadProcessId(hwnd, &processId);

        if (processId == *(DWORD*)lParam) {
            // Check if this is a visible main window (not a child window)
            if (IsWindowVisible(hwnd) && GetParent(hwnd) == NULL) {
                char windowTitle[256];
                GetWindowText(hwnd, windowTitle, sizeof(windowTitle));

                // Store the first main window we find
                g_gameWindow = hwnd;
                return FALSE; // Stop enumeration
            }
        }
        return TRUE; // Continue enumeration
    }, (LPARAM)&currentProcessId);
}

void SendKeypressToProcess(int virtualKey) {
    // Make sure we have the game window
    FindGameWindow();

    if (g_gameWindow && IsWindow(g_gameWindow)) {
        // Send the keypress directly to the game window
        // Use WM_KEYDOWN and WM_KEYUP for more reliable input
        PostMessage(g_gameWindow, WM_KEYDOWN, virtualKey, 0);
        Sleep(50); // Small delay between key down and up
        PostMessage(g_gameWindow, WM_KEYUP, virtualKey, 0);
    } else {
        // Fallback: Use SendInput but with focus check
        HWND foregroundWindow = GetForegroundWindow();
        if (foregroundWindow) {
            DWORD windowProcessId;
            GetWindowThreadProcessId(foregroundWindow, &windowProcessId);

            // Only send input if the foreground window belongs to our process
            if (windowProcessId == GetCurrentProcessId()) {
                INPUT inputs[2] = {};

                // Key down
                inputs[0].type = INPUT_KEYBOARD;
                inputs[0].ki.wVk = virtualKey;
                inputs[0].ki.dwFlags = 0;

                // Key up
                inputs[1].type = INPUT_KEYBOARD;
                inputs[1].ki.wVk = virtualKey;
                inputs[1].ki.dwFlags = KEYEVENTF_KEYUP;

                SendInput(2, inputs, sizeof(INPUT));
            }
        }
    }
}

bool SkillIsInCooldown(int skillID) {
	DWORD pSkill = Engine::Skill::FindSkill(skillID);
	if (!pSkill)
		return true; // Assume skill is in cooldown if not found
	
	try {
		if (Engine::Skill::WantShowTimer((DWORD*)pSkill))
			return true;
	} catch (...) {}

	return false;
}
void UseRandomAvailableSkill(TargetInfo myTarget) {
	// Check if player is valid and alive
	PlayerInfo player;
	if (!player.IsValid() || !player.IsAlive()) {
		return;
	}

    if (myTarget.IsValid() && myTarget.IsAlive()) {


        // If no valid skills found, return
        if (skillIDs.empty()) {
            return;
        }

        // Create random number generator
        static std::random_device rd;
        static std::mt19937 gen(rd());

        // Try to find an available skill (not in cooldown)
        std::vector<int> availableSkills;
        for (int skillID : skillIDs) {
            if (!SkillIsInCooldown(skillID)) {
                availableSkills.push_back(skillID);
            }
        }

        // If we have available skills, use a random one
        if (!availableSkills.empty()) {
            std::uniform_int_distribution<> dis(0, availableSkills.size() - 1);
            int randomIndex = dis(gen);
            int selectedSkillID = availableSkills[randomIndex];

            // Use the selected skill
            Engine::Skill::Send_Skill(selectedSkillID, 1);
            //Sleep(1000);
            //addinfomessage with the skill id
			//Engine::KGameSys::AddInfoMessage(("[AB] Using skill ID: " + std::to_string(selectedSkillID)).c_str(), 0x00FF00, 0);
        }
    }

}
int lastTargetBeheadedID = 0;
void AutoAttack() {
    PlayerInfo player;

    if (!player.IsValid()) {
        return;
    }

    if (!player.IsAlive()) {
        return;
    }

    if (g_Config.AutoBehead) {
        TargetInfo nearestTarget = GetNearestTarget(false);
        if (!nearestTarget.IsAlive() && nearestTarget.IsValid() && lastTargetBeheadedID != nearestTarget.GetTargetID()) {
                Engine::Skill::Send_Skill(1, 0);
				lastTargetBeheadedID = nearestTarget.GetTargetID();
        }
    }
    // Get the nearest target

    TargetInfo nearestTarget = GetNearestTarget(true);
    if (!nearestTarget.IsValid() || !nearestTarget.IsAlive())
        return;
	
    try {
        // Set the current target
        DWORD targetID = nearestTarget.GetTargetID();
        *(int*)Engine::CGame_Character::CurTarget = Engine::CGame_Character::FindCharacter((int)targetID, 0);

        Engine::KGameSys::SendAttack();
        Engine::KGameSys::SelUID(targetID, 1, 0);


        if (g_Config.AutoSkill && nearestTarget.GetHPPercentage() <= 90.0f) {
            UseRandomAvailableSkill(nearestTarget);
            Sleep(1500);
        }



    }
    catch (...) {
        Engine::KGameSys::AddInfoMessage("[AB] AutoAttack: Exception occurred during attack", 0xFF0000, 0);
        printf("AutoAttack: Exception occurred\n");
    }
}

void InitializeKeyboardMonitor()
{
    // Initialize key states for F2-F12
    for (int key = VK_F2; key <= VK_F12; key++)
    {
        g_KeyStates[key] = false;
    }

    // Create keyboard monitoring thread
    g_hKeyMonitorThread = CreateThread(nullptr, 0, KeyboardMonitorThread, nullptr, 0, nullptr);
    if (g_hKeyMonitorThread)
    {
        Engine::KGameSys::AddInfoMessage("[AB] Keyboard monitor started successfully", 0x00FF00, 0);
    }
    else
    {
        Engine::KGameSys::AddInfoMessage("[AB] Failed to start keyboard monitor", 0xFF0000, 0);
    }
}

void CleanupKeyboardMonitor()
{
    g_bRunning = false;

    if (g_hKeyMonitorThread)
    {
        WaitForSingleObject(g_hKeyMonitorThread, 2000);
        CloseHandle(g_hKeyMonitorThread);
        g_hKeyMonitorThread = nullptr;
    }

    StopAllBotThreads();
}

DWORD WINAPI KeyboardMonitorThread(LPVOID lpParam)
{
    //Engine::KGameSys::AddInfoMessage("[AB] Keyboard monitor thread started", 0x00FF00, 0);

    while (g_bRunning)
    {
        // Check each function key F2-F12
        for (int key = VK_F2; key <= VK_F12; key++)
        {
            bool currentState = (GetAsyncKeyState(key) & 0x8000) != 0;
            bool previousState = g_KeyStates[key];

            // Detect key press (transition from not pressed to pressed)
            if (currentState && !previousState)
            {
                g_KeyStates[key] = true;

                // Handle key press
                switch (key)
                {
                case VK_F2:
                    LoadConfig();
                    break;
                case VK_F3:
                    StartBotThread(VK_F3);
                    break;
                case VK_F4:
                    StartBotThread(VK_F4);
                    break;
                case VK_F5:
					StartBotThread(VK_F5);
                    break;
                case VK_F6:
                    StartBotThread(VK_F6);
                    break;
                case VK_F7:
                    StartBotThread(VK_F7);
                    break;
                case VK_F8:
                    StartBotThread(VK_F8);
                    break;
                case VK_F9:
                    StartBotThread(VK_F9);
                    break;
                case VK_F10:
                    StartBotThread(VK_F10);
                    break;
                case VK_F11:
                    StartBotThread(VK_F11);
                    break;
                }
            }
            else if (!currentState && previousState)
            {
                // Key released
                g_KeyStates[key] = false;
            }
        }

        Sleep(50); // Check every 50ms to avoid excessive CPU usage
    }

    Engine::KGameSys::AddInfoMessage("[AB] Keyboard monitor thread stopped", 0xFFFF00, 0);
    return 0;
}

void StartBotThread(int functionKey)
{
    std::string keyName = "F" + std::to_string(functionKey - VK_F1 + 1);

    // Check if bot is already running for this key
    if (g_BotRunning[functionKey])
    {
        // Stop the bot
        StopBotThread(functionKey);
        return;
    }

    // Start appropriate bot based on function key
    switch (functionKey)
    {
    case VK_F8:
        g_BotRunning[functionKey] = true;
        g_BotThreads[functionKey] = std::thread(AutoAttackBot, functionKey);
        Engine::KGameSys::AddInfoMessage(("[AB] " + keyName + " - AutoAttack Started").c_str(), 0x00FF00, 0);
        break;
    case VK_F9:
        g_BotRunning[functionKey] = true;
        g_BotThreads[functionKey] = std::thread(AutoHealAndManaBot, functionKey);
        Engine::KGameSys::AddInfoMessage(("[AB] " + keyName + " - Auto Heal & Mana Started").c_str(), 0x00FF00, 0);
        break;
    }
}

void StopBotThread(int functionKey)
{
    // Signal the bot to stop
    g_BotRunning[functionKey] = false;

    auto it = g_BotThreads.find(functionKey);
    if (it != g_BotThreads.end())
    {
        if (it->second.joinable())
        {
            it->second.detach(); // Don't block - let it finish naturally
        }
        g_BotThreads.erase(it);

        std::string keyName = "F" + std::to_string(functionKey - VK_F1 + 1);
        Engine::KGameSys::AddInfoMessage(("[AB] " + keyName + " - Stopped").c_str(), 0x0000FF, 0);
    }
}

void StopAllBotThreads()
{
    // Signal all bots to stop
    for (auto& pair : g_BotRunning)
    {
        pair.second = false;
    }

    // Detach all threads (don't block)
    for (auto& pair : g_BotThreads)
    {
        if (pair.second.joinable())
        {
            pair.second.detach();
        }
    }
    g_BotThreads.clear();
    g_BotRunning.clear();
}

void AutoAttackBot(int functionKey)
{
    while (g_BotRunning[functionKey] && g_bRunning)
    {
		AutoAttack();
        Sleep(1000);
    }
}

void AutoHealAndManaBot(int functionKey)
{
    while (g_BotRunning[functionKey] && g_bRunning)
    {
        PlayerInfo player;

        // Check if player is valid and alive
        if (!player.IsValid() || !player.IsAlive()) {
            Sleep(1000);
            continue;
        }

        // Auto Health - Check if HP is at or below 90%
        if (g_Config.AutoHeal && player.GetHPPercentage() <= 90.0f) {
            SendKeypressToProcess(g_Config.HealthKey + 0x30); // Convert to VK code (1-9 -> VK_1-VK_9)
            //Engine::KGameSys::AddInfoMessage(("[AB] Auto Health: Used health potion (HP: " + std::to_string((int)player.GetHPPercentage()) + "%)").c_str(), 0x00FF00, 0);
            Sleep(1500); // Cooldown to prevent spam
        }

        // Auto Mana - Check if MP is at or below 60%
        if (g_Config.AutoMana && player.GetManaPercentage() <= 60.0f) {
            SendKeypressToProcess(g_Config.ManaKey + 0x30); // Convert to VK code (1-9 -> VK_1-VK_9)
            //Engine::KGameSys::AddInfoMessage(("[AB] Auto Mana: Used mana potion (MP: " + std::to_string((int)player.GetManaPercentage()) + "%)").c_str(), 0x0099FF, 0);
            Sleep(1500); // Cooldown to prevent spam
        }

        Sleep(500); // Check every 500ms
    }
}

//void AutoManaBot(int functionKey)
//{
//    while (g_BotRunning[functionKey] && g_bRunning)
//    {
//        // TODO: Implement auto mana logic
//        // Check mana, use mana key if needed
//        Sleep(1000); // Prevent excessive CPU usage
//    }
//}
//
//void AutoSkillBot(int functionKey)
//{
//    while (g_BotRunning[functionKey] && g_bRunning)
//    {
//        // TODO: Implement auto skill logic
//        // Use skills based on configuration
//        Sleep(1000); // Prevent excessive CPU usage
//    }
//}
