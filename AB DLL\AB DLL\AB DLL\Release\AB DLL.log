﻿  MemoryReader.cpp
  dllmain.cpp
     Creating library G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.lib and object G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.exp
  Generating code
  30 of 650 functions ( 4.6%) were compiled, the rest were copied from previous compilation.
    2 functions were new in current compilation
    61 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  AB DLL.vcxproj -> G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.dll
