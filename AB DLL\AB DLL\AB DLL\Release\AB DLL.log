﻿  dllmain.cpp
  MemoryReader.cpp
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(236,17): error C2664: 'int GetWindowTextW(HWND,LPWSTR,int)': cannot convert argument 2 from 'char [256]' to 'LPWSTR'
  (compiling source file '/MemoryReader.cpp')
      G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(236,37):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h(8950,1):
      see declaration of 'GetWindowTextW'
      G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(236,17):
      while trying to match the argument list '(<PERSON><PERSON><PERSON>, char [256], unsigned int)'
  
