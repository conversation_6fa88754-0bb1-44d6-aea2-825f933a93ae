﻿  MemoryReader.cpp
     Creating library G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.lib and object G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.exp
  Generating code
  1 of 643 functions ( 0.2%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    1 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  AB DLL.vcxproj -> G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.dll
