namespace Heth_AB
{
    partial class ModernInjector
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ModernInjector));
            this.lblTitle = new System.Windows.Forms.Label();
            this.lblProcesses = new System.Windows.Forms.Label();
            this.cmbProcesses = new System.Windows.Forms.ComboBox();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.lblDllSelection = new System.Windows.Forms.Label();
            this.cmbDllSelection = new System.Windows.Forms.ComboBox();
            this.btnBrowseDll = new System.Windows.Forms.Button();
            this.btnInject = new System.Windows.Forms.Button();
            this.lblStatus = new System.Windows.Forms.Label();
            this.rtbStatus = new System.Windows.Forms.RichTextBox();
            this.lblDllPath = new System.Windows.Forms.Label();
            this.lblVersion = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new System.Drawing.Font("Segoe UI", 16F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(150)))), ((int)(((byte)(255)))));
            this.lblTitle.Location = new System.Drawing.Point(12, 9);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(302, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "Heth AI - Advanced Injector";
            // 
            // lblProcesses
            // 
            this.lblProcesses.AutoSize = true;
            this.lblProcesses.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblProcesses.Location = new System.Drawing.Point(12, 60);
            this.lblProcesses.Name = "lblProcesses";
            this.lblProcesses.Size = new System.Drawing.Size(96, 15);
            this.lblProcesses.TabIndex = 1;
            this.lblProcesses.Text = "Target Processes:";
            // 
            // cmbProcesses
            // 
            this.cmbProcesses.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbProcesses.FormattingEnabled = true;
            this.cmbProcesses.Location = new System.Drawing.Point(12, 78);
            this.cmbProcesses.Name = "cmbProcesses";
            this.cmbProcesses.Size = new System.Drawing.Size(400, 23);
            this.cmbProcesses.TabIndex = 2;
            // 
            // btnRefresh
            // 
            this.btnRefresh.Location = new System.Drawing.Point(418, 78);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(80, 23);
            this.btnRefresh.TabIndex = 3;
            this.btnRefresh.Text = "Refresh";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // lblDllSelection
            // 
            this.lblDllSelection.AutoSize = true;
            this.lblDllSelection.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblDllSelection.Location = new System.Drawing.Point(12, 115);
            this.lblDllSelection.Name = "lblDllSelection";
            this.lblDllSelection.Size = new System.Drawing.Size(64, 15);
            this.lblDllSelection.TabIndex = 4;
            this.lblDllSelection.Text = "Select DLL:";
            // 
            // cmbDllSelection
            // 
            this.cmbDllSelection.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDllSelection.FormattingEnabled = true;
            this.cmbDllSelection.Location = new System.Drawing.Point(12, 135);
            this.cmbDllSelection.Name = "cmbDllSelection";
            this.cmbDllSelection.Size = new System.Drawing.Size(320, 23);
            this.cmbDllSelection.TabIndex = 5;
            // 
            // btnBrowseDll
            // 
            this.btnBrowseDll.Location = new System.Drawing.Point(338, 135);
            this.btnBrowseDll.Name = "btnBrowseDll";
            this.btnBrowseDll.Size = new System.Drawing.Size(80, 23);
            this.btnBrowseDll.TabIndex = 6;
            this.btnBrowseDll.Text = "Browse...";
            this.btnBrowseDll.UseVisualStyleBackColor = true;
            this.btnBrowseDll.Click += new System.EventHandler(this.btnBrowseDll_Click);
            // 
            // btnInject
            // 
            this.btnInject.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.btnInject.Location = new System.Drawing.Point(12, 172);
            this.btnInject.Name = "btnInject";
            this.btnInject.Size = new System.Drawing.Size(486, 35);
            this.btnInject.TabIndex = 7;
            this.btnInject.Text = "🚀 INJECT DLL";
            this.btnInject.UseVisualStyleBackColor = true;
            this.btnInject.Click += new System.EventHandler(this.btnInject_Click);
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblStatus.Location = new System.Drawing.Point(12, 222);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(83, 15);
            this.lblStatus.TabIndex = 8;
            this.lblStatus.Text = "Status Output:";
            // 
            // rtbStatus
            // 
            this.rtbStatus.Font = new System.Drawing.Font("Consolas", 8.25F);
            this.rtbStatus.Location = new System.Drawing.Point(12, 240);
            this.rtbStatus.Name = "rtbStatus";
            this.rtbStatus.ReadOnly = true;
            this.rtbStatus.Size = new System.Drawing.Size(486, 200);
            this.rtbStatus.TabIndex = 6;
            this.rtbStatus.Text = "[00:00:00] Heth AB Advanced Injector initialized\n[00:00:00] Ready for injection.." +
    ".";
            // 
            // lblDllPath
            // 
            this.lblDllPath.AutoSize = true;
            this.lblDllPath.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.lblDllPath.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.lblDllPath.Location = new System.Drawing.Point(12, 452);
            this.lblDllPath.Name = "lblDllPath";
            this.lblDllPath.Size = new System.Drawing.Size(81, 13);
            this.lblDllPath.TabIndex = 7;
            this.lblDllPath.Text = "DLL: AB DLL.dll";
            // 
            // lblVersion
            // 
            this.lblVersion.AutoSize = true;
            this.lblVersion.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.lblVersion.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(180)))), ((int)(((byte)(180)))), ((int)(((byte)(180)))));
            this.lblVersion.Location = new System.Drawing.Point(418, 452);
            this.lblVersion.Name = "lblVersion";
            this.lblVersion.Size = new System.Drawing.Size(72, 13);
            this.lblVersion.TabIndex = 8;
            this.lblVersion.Text = "Version 2.0.0";
            // 
            // ModernInjector
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(32)))), ((int)(((byte)(32)))), ((int)(((byte)(32)))));
            this.ClientSize = new System.Drawing.Size(510, 477);
            this.Controls.Add(this.lblVersion);
            this.Controls.Add(this.lblDllPath);
            this.Controls.Add(this.rtbStatus);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.btnInject);
            this.Controls.Add(this.btnBrowseDll);
            this.Controls.Add(this.cmbDllSelection);
            this.Controls.Add(this.lblDllSelection);
            this.Controls.Add(this.btnRefresh);
            this.Controls.Add(this.cmbProcesses);
            this.Controls.Add(this.lblProcesses);
            this.Controls.Add(this.lblTitle);
            this.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.ForeColor = System.Drawing.Color.White;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.Name = "ModernInjector";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Heth AI";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Label lblProcesses;
        private System.Windows.Forms.ComboBox cmbProcesses;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Label lblDllSelection;
        private System.Windows.Forms.ComboBox cmbDllSelection;
        private System.Windows.Forms.Button btnBrowseDll;
        private System.Windows.Forms.Button btnInject;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.RichTextBox rtbStatus;
        private System.Windows.Forms.Label lblDllPath;
        private System.Windows.Forms.Label lblVersion;
    }
}
