#pragma once
#include "pch.h"
#include <windows.h>
#include <fstream>
#include <string>
#include <map>
#include <thread>
//#pragma comment(lib,"detours.lib")
//#include "Detours/detours.h"

// Game engine namespaces and function pointers
namespace Engine
{
    
    namespace CGame_Character
    {
        static unsigned long* m_Master = (unsigned long*)0x00906824;
        static unsigned long(__cdecl* FindCharacter)(unsigned int nID, int) = (unsigned long(__cdecl*)(unsigned int, int))0x00428490;

        // Player memory addresses and offsets
        static const DWORD NameAdd = 0x009EFA44;
        static const DWORD HealthX = 0x009EFC38;
        static const DWORD HealthY = 0x009068B8;
        static const DWORD MaxHealthAddresse = 0x009EF95C;
        static const DWORD MaxManaAddresse = 0x009EF960;
        static const DWORD CurrManaAddresse = 0x009EF958;
        static const DWORD PlayerXOffset = 0x473C;
        static const DWORD PlayerYOffset = 0x4744;
		static const DWORD CurTarget = 0x00906850;
        

    }

    namespace KGameSys
    {
        static void(__cdecl* AddChattingMessage)(char Type, const char* Message, int Color) = (void(__cdecl*)(char, const char*, int))0x006E72C0;
        static void(__cdecl* AddInfoMessage)(const char* Message, int Color, int type) = (void(__cdecl*)(const char*, int, int))0x006E72F0;
        static int(__cdecl* OpenWindow)(const char* name, int Argument, int Value, int Type, int nForce, int x) = (int(__cdecl*)(const char*, int, int, int, int, int))0x006DACC0;
        //send skill 0x0042E360 BOOL __cdecl CGame_Character::Send_Skill(BYTE SkillID, BYTE Arg)
		
        static bool(__cdecl * Chk_SkillCast)() = (bool(__cdecl*)())0x0042DF80;
        static bool(__cdecl* SendAttack)() = (bool(__cdecl*)())0x006DF6D0;
		static void(__cdecl* SelUID)(int TargetID, int a2, int a3) = (void(__cdecl*)(int, int, int))0x006E3F00;
        
    }
    static DWORD EntityIDs = 0x00906854;

    namespace Target {
        static const DWORD TargetPosXOffset = 0x473C;
        static const DWORD TargetPosYOffset = 0x4744;
        static const DWORD TargetIDOffset = 0x4444;
        static const DWORD TargetMinHPOffset = 0x47B8;
        static const DWORD TargetMaxHPOffset = 0x47BC;
		static const DWORD TargetKindOffset = 0x4734;
    }

    namespace Skill {
		static DWORD(__cdecl* FindSkill)(int bySkill) = (DWORD(__cdecl*)(int))0x004DA670;
		static bool(__thiscall* WantShowTimer)(DWORD* thisPtr) = (bool(__thiscall*)(DWORD*))0x004DF250;
        static bool(__cdecl* Send_Skill)(int SkillID, int Arg) = (bool(__cdecl*)(int, int))0x0042E360;
        
    }
}


class TargetInfo
{
private:
    // Target memory offsets


public:
    TargetInfo() : m_pTarget(nullptr) {}
    ~TargetInfo() {} // no delete since we don't own or touch it
    void* m_pTarget; // opaque pointer to target

    // Helper method to safely read memory
    template<typename T>
    T ReadMemory(DWORD offset, T defaultValue = T{}) const {
        if (!m_pTarget) return defaultValue;

        try {
            return *(T*)((DWORD)m_pTarget + offset);
        }
        catch (...) {
            return defaultValue;
        }
    }

    // Convenient property accessors
    int GetPosX() const { return ReadMemory<int>(Engine::Target::TargetPosXOffset, 0); }
    int GetPosY() const { return ReadMemory<int>(Engine::Target::TargetPosYOffset, 0); }
    DWORD GetTargetID() const { return ReadMemory<DWORD>(Engine::Target::TargetIDOffset, 0); }
    DWORD GetCurrentHP() const { return ReadMemory<DWORD>(Engine::Target::TargetMinHPOffset, 0); }
    DWORD GetMaxHP() const { return ReadMemory<DWORD>(Engine::Target::TargetMaxHPOffset, 0); }

    // Additional utility methods
    float GetHPPercentage() const {
        DWORD maxHP = GetMaxHP();
        if (maxHP == 0) return 0.0f;
        return (float)GetCurrentHP() / (float)maxHP * 100.0f;
    }

    bool IsValid() const { return m_pTarget != nullptr; }
    bool IsAlive() const { return GetCurrentHP() > 0; }

    // Get distance from a point (useful for finding nearest target)
    float GetDistanceFrom(float x, float y) const {
        float dx = GetPosX() - x;
        float dy = GetPosY() - y;
        return sqrt(dx * dx + dy * dy);
    }
    // target is Monster
	bool IsMonster() const {
		if (!m_pTarget) return false;
		BYTE kind = ReadMemory<BYTE>(Engine::Target::TargetKindOffset, 0);
		return (kind == 1);
	}
};

class PlayerInfo
{
private:


public:
    PlayerInfo() {}
    ~PlayerInfo() {}

    // Helper method to safely read memory from absolute addresses
    template<typename T>
    T ReadMemoryAbsolute(DWORD address, T defaultValue = T{}) const {
        try {
            return *(T*)address;
        }
        catch (...) {
            return defaultValue;
        }
    }

    // Helper method to safely read memory from player pointer + offset
    template<typename T>
    T ReadMemoryOffset(DWORD offset, T defaultValue = T{}) const {
        try {
            unsigned long* masterPtr = Engine::CGame_Character::m_Master;
            if (!masterPtr || !*masterPtr) return defaultValue;

            return *(T*)(*masterPtr + offset);
        }
        catch (...) {
            return defaultValue;
        }
    }

    // Player property accessors
    const char* GetName() const {
        const char* namePtr = ReadMemoryAbsolute<const char*>(Engine::CGame_Character::NameAdd, nullptr);
        return namePtr ? namePtr : "Unknown";
    }

    int GetCurrentHP() const {
        int healthX = ReadMemoryAbsolute<int>(Engine::CGame_Character::HealthX, 0);
        int healthY = ReadMemoryAbsolute<int>(Engine::CGame_Character::HealthY, 0);
        return healthX ^ healthY; // XOR operation as specified
    }

    int GetMaxHP() const { return ReadMemoryAbsolute<int>(Engine::CGame_Character::MaxHealthAddresse, 0); }
    int GetMaxMana() const { return ReadMemoryAbsolute<int>(Engine::CGame_Character::MaxManaAddresse, 0); }
    int GetCurrentMana() const { return ReadMemoryAbsolute<int>(Engine::CGame_Character::CurrManaAddresse, 0); }

    // Position using offsets from master pointer
    int GetPosX() const { return ReadMemoryOffset<int>(Engine::CGame_Character::PlayerXOffset, 0); }
    int GetPosY() const { return ReadMemoryOffset<int>(Engine::CGame_Character::PlayerYOffset, 0); }

    // Utility methods
    float GetHPPercentage() const {
        int maxHP = GetMaxHP();
        if (maxHP == 0) return 0.0f;
        return (float)GetCurrentHP() / (float)maxHP * 100.0f;
    }

    float GetManaPercentage() const {
        int maxMana = GetMaxMana();
        if (maxMana == 0) return 0.0f;
        return (float)GetCurrentMana() / (float)maxMana * 100.0f;
    }

    bool IsValid() const {
        unsigned long* masterPtr = Engine::CGame_Character::m_Master;
        return masterPtr && *masterPtr;
    }

    bool IsAlive() const { return GetCurrentHP() > 0; }

    // Get distance from a point
    float GetDistanceFrom(int x, int y) const {
        int dx = GetPosX() - x;
        int dy = GetPosY() - y;
        return sqrt((float)(dx * dx + dy * dy));
    }

    // Get distance from a target
    float GetDistanceFromTarget(const TargetInfo& target) const {
        return GetDistanceFrom((int)target.GetPosX(), (int)target.GetPosY());
    }
};

// Configuration structure
struct Config {
    bool AutoHeal = true;
    bool AutoMana = true;
    bool AutoSkill = false;
    bool AutoBehead = true;
    int ManaKey = 3;
    int HealthKey = 4;
    std::string Skills = "5,16,17,24,21,10";
};

// Global variables
extern Config g_Config;
extern std::map<int, std::thread> g_BotThreads;
extern std::map<int, bool> g_BotRunning;
extern std::map<int, bool> g_KeyStates;
extern bool g_bRunning;
extern HANDLE g_hKeyMonitorThread;
extern HWND g_gameWindow;

// Configuration functions
void LoadConfig();
void SaveConfig();

// Keyboard monitoring functions
void InitializeKeyboardMonitor();
void CleanupKeyboardMonitor();
DWORD WINAPI KeyboardMonitorThread(LPVOID lpParam);

// Bot thread functions
void StartBotThread(int functionKey);
void StopBotThread(int functionKey);
void StopAllBotThreads();

// Individual bot functions (to be implemented)
void AutoAttackBot(int functionKey);
void AutoHealAndManaBot(int functionKey);
//void AutoSkillBot(int functionKey);
void ShowConsole();
TargetInfo GetNearestTarget(bool chk_isAlive);
void DisplayPlayerInfo();
void AutoAttack();
void FindGameWindow();
void SendKeypressToProcess(int virtualKey);