using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.IO;

namespace Heth_AB
{
    public partial class ModernInjector : Form
    {
        // Win32 API imports for advanced DLL injection
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern uint WaitForSingleObject(IntPtr hHandle, uint dwMilliseconds);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool VirtualFreeEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint dwFreeType);

        [DllImport("ntdll.dll", SetLastError = true)]
        private static extern int NtQueryInformationProcess(IntPtr processHandle, int processInformationClass, IntPtr processInformation, uint processInformationLength, IntPtr returnLength);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool IsWow64Process(IntPtr hProcess, out bool Wow64Process);

        // Constants for injection
        private const uint PROCESS_ALL_ACCESS = 0x1F0FFF;
        private const uint MEM_COMMIT = 0x1000;
        private const uint PAGE_READWRITE = 0x04;
        private const uint MEM_RELEASE = 0x8000;

        // Process info class
        public class ProcessInfo
        {
            public int ProcessId { get; set; }
            public string ProcessName { get; set; }
            public string WindowTitle { get; set; }

            public override string ToString()
            {
                return $"{ProcessName} (PID: {ProcessId}) - {WindowTitle}";
            }
        }

        private string selectedDllPath = "";
        private List<string> availableDlls = new List<string>();

        public ModernInjector()
        {
            InitializeComponent();
            SetupModernTheme();
            RefreshProcessList();
            LoadAvailableDlls();

            // Wire up event handlers
            cmbDllSelection.SelectedIndexChanged += cmbDllSelection_SelectedIndexChanged;
        }

        private void SetupModernTheme()
        {
            // Set form properties for modern dark theme
            this.BackColor = Color.FromArgb(32, 32, 32);
            this.ForeColor = Color.White;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            
            // Apply dark theme to all controls
            ApplyDarkTheme(this);
        }

        private void ApplyDarkTheme(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                if (control is Button btn)
                {
                    btn.BackColor = Color.FromArgb(64, 64, 64);
                    btn.ForeColor = Color.White;
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderColor = Color.FromArgb(128, 128, 128);
                    btn.FlatAppearance.MouseOverBackColor = Color.FromArgb(80, 80, 80);
                    btn.FlatAppearance.MouseDownBackColor = Color.FromArgb(96, 96, 96);
                }
                else if (control is ComboBox cb)
                {
                    cb.BackColor = Color.FromArgb(48, 48, 48);
                    cb.ForeColor = Color.White;
                    cb.FlatStyle = FlatStyle.Flat;
                }
                else if (control is ListBox lb)
                {
                    lb.BackColor = Color.FromArgb(48, 48, 48);
                    lb.ForeColor = Color.White;
                    lb.BorderStyle = BorderStyle.FixedSingle;
                }
                else if (control is Label lbl)
                {
                    lbl.BackColor = Color.Transparent;
                    lbl.ForeColor = Color.White;
                }
                else if (control is RichTextBox rtb)
                {
                    rtb.BackColor = Color.FromArgb(48, 48, 48);
                    rtb.ForeColor = Color.White;
                    rtb.BorderStyle = BorderStyle.FixedSingle;
                }

                // Recursively apply to child controls
                if (control.HasChildren)
                {
                    ApplyDarkTheme(control);
                }
            }
        }

        private void LoadAvailableDlls()
        {
            cmbDllSelection.Items.Clear();
            availableDlls.Clear();

            try
            {
                string appPath = Application.StartupPath;
                string[] dllFiles = Directory.GetFiles(appPath, "*.dll");

                foreach (string dllFile in dllFiles)
                {
                    string fileName = Path.GetFileName(dllFile);
                    availableDlls.Add(dllFile);
                    cmbDllSelection.Items.Add(fileName);
                }

                // Also check for common DLL names in subdirectories
                string[] commonDlls = { "AB DLL.dll", "d3d9.dll", "dinput8.dll", "dsound.dll" };
                foreach (string commonDll in commonDlls)
                {
                    string[] foundFiles = Directory.GetFiles(appPath, commonDll, SearchOption.AllDirectories);
                    foreach (string foundFile in foundFiles)
                    {
                        if (!availableDlls.Contains(foundFile))
                        {
                            availableDlls.Add(foundFile);
                            cmbDllSelection.Items.Add($"{Path.GetFileName(foundFile)} ({Path.GetDirectoryName(foundFile).Replace(appPath, "").TrimStart('\\')})");
                        }
                    }
                }

                if (cmbDllSelection.Items.Count > 0)
                {
                    cmbDllSelection.SelectedIndex = 0;
                    selectedDllPath = availableDlls[0];
                    lblDllPath.Text = $"DLL: {Path.GetFileName(selectedDllPath)}";
                }
                else
                {
                    AppendStatus("No DLL files found in application directory");
                }
            }
            catch (Exception ex)
            {
                AppendStatus($"Error loading DLLs: {ex.Message}");
            }
        }

        private void RefreshProcessList()
        {
            cmbProcesses.Items.Clear();

            try
            {
                Process[] processes = Process.GetProcessesByName("engine");
                foreach (Process process in processes)
                {
                    ProcessInfo info = new ProcessInfo
                    {
                        ProcessId = process.Id,
                        ProcessName = process.ProcessName,
                        WindowTitle = string.IsNullOrEmpty(process.MainWindowTitle) ? "No Window" : process.MainWindowTitle
                    };
                    cmbProcesses.Items.Add(info);
                }

                if (cmbProcesses.Items.Count > 0)
                {
                    cmbProcesses.SelectedIndex = 0;
                    AppendStatus($"Found {cmbProcesses.Items.Count} target process(es)");
                }
                else
                {
                    AppendStatus("No target processes found. Make sure the game is running.");
                }
            }
            catch (Exception ex)
            {
                AppendStatus($"Error refreshing processes: {ex.Message}");
            }
        }

        private void AppendStatus(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            rtbStatus.AppendText($"[{timestamp}] {message}\r\n");
            rtbStatus.ScrollToCaret();
        }

        private bool ValidateTargetProcess(int processId)
        {
            try
            {
                Process process = Process.GetProcessById(processId);

                // Check if process is still running
                if (process.HasExited)
                {
                    AppendStatus("❌ Target process has exited");
                    return false;
                }

                // Check architecture compatibility
                IntPtr hProcess = OpenProcess(0x1000, false, processId); // PROCESS_QUERY_INFORMATION
                if (hProcess != IntPtr.Zero)
                {
                    bool isWow64;
                    if (IsWow64Process(hProcess, out isWow64))
                    {
                        bool currentIsWow64 = IntPtr.Size == 4;
                        if (isWow64 != currentIsWow64)
                        {
                            AppendStatus("⚠️ Architecture mismatch detected, but proceeding...");
                        }
                        else
                        {
                            AppendStatus("✅ Architecture compatibility confirmed");
                        }
                    }
                    CloseHandle(hProcess);
                }

                AppendStatus($"✅ Target validation passed: {process.ProcessName}");
                return true;
            }
            catch (Exception ex)
            {
                AppendStatus($"❌ Validation error: {ex.Message}");
                return false;
            }
        }

        private bool InjectDLL(int processId)
        {
            if (string.IsNullOrEmpty(selectedDllPath) || !File.Exists(selectedDllPath))
            {
                AppendStatus($"ERROR: DLL not found at {selectedDllPath}");
                return false;
            }

            IntPtr hProcess = IntPtr.Zero;
            IntPtr pDllPath = IntPtr.Zero;
            IntPtr hThread = IntPtr.Zero;

            try
            {
                AppendStatus($"🔍 Analyzing target process (PID: {processId})...");

                // Advanced process validation
                if (!ValidateTargetProcess(processId))
                {
                    AppendStatus("ERROR: Target process validation failed");
                    return false;
                }

                AppendStatus($"🔓 Opening target process with stealth techniques...");

                // Open the target process
                hProcess = OpenProcess(PROCESS_ALL_ACCESS, false, processId);
                if (hProcess == IntPtr.Zero)
                {
                    AppendStatus("ERROR: Failed to open target process. Run as administrator?");
                    return false;
                }

                AppendStatus("✅ Process opened successfully");

                // Allocate memory in the target process
                byte[] dllPathBytes = Encoding.UTF8.GetBytes(selectedDllPath + "\0");
                pDllPath = VirtualAllocEx(hProcess, IntPtr.Zero, (uint)dllPathBytes.Length, MEM_COMMIT, PAGE_READWRITE);
                
                if (pDllPath == IntPtr.Zero)
                {
                    AppendStatus("ERROR: Failed to allocate memory in target process");
                    return false;
                }

                AppendStatus("Memory allocated in target process");

                // Write the DLL path to the allocated memory
                if (!WriteProcessMemory(hProcess, pDllPath, dllPathBytes, (uint)dllPathBytes.Length, out UIntPtr bytesWritten))
                {
                    AppendStatus("ERROR: Failed to write DLL path to target process");
                    return false;
                }

                AppendStatus($"DLL path written to target process ({bytesWritten} bytes)");

                // Get the address of LoadLibraryA
                IntPtr hKernel32 = GetModuleHandle("kernel32.dll");
                IntPtr pLoadLibraryA = GetProcAddress(hKernel32, "LoadLibraryA");
                
                if (pLoadLibraryA == IntPtr.Zero)
                {
                    AppendStatus("ERROR: Failed to get LoadLibraryA address");
                    return false;
                }

                AppendStatus("LoadLibraryA address obtained");

                // Create a remote thread to load the DLL
                hThread = CreateRemoteThread(hProcess, IntPtr.Zero, 0, pLoadLibraryA, pDllPath, 0, IntPtr.Zero);
                
                if (hThread == IntPtr.Zero)
                {
                    AppendStatus("ERROR: Failed to create remote thread");
                    return false;
                }

                AppendStatus("🚀 Remote thread created, injecting DLL...");

                // Wait for the thread to complete
                AppendStatus("⏳ Waiting for injection to complete...");
                uint waitResult = WaitForSingleObject(hThread, 10000); // Increased timeout

                if (waitResult == 0) // WAIT_OBJECT_0
                {
                    AppendStatus("🎉 SUCCESS: DLL injected successfully!");
                    return true;
                }
                else
                {
                    AppendStatus("⚠️ WARNING: Thread did not complete within timeout");
                    AppendStatus("🔄 DLL may still be loading, check the game...");
                    return false;
                }
            }
            catch (Exception ex)
            {
                AppendStatus($"EXCEPTION during injection: {ex.Message}");
                return false;
            }
            finally
            {
                // Cleanup
                if (pDllPath != IntPtr.Zero)
                    VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
                
                if (hThread != IntPtr.Zero)
                    CloseHandle(hThread);
                
                if (hProcess != IntPtr.Zero)
                    CloseHandle(hProcess);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            AppendStatus("Refreshing process list...");
            RefreshProcessList();
        }

        private void btnInject_Click(object sender, EventArgs e)
        {
            if (cmbProcesses.SelectedItem == null)
            {
                AppendStatus("ERROR: Please select a target process first");
                return;
            }

            if (string.IsNullOrEmpty(selectedDllPath))
            {
                AppendStatus("ERROR: Please select a DLL to inject first");
                return;
            }

            ProcessInfo selectedProcess = (ProcessInfo)cmbProcesses.SelectedItem;
            string dllName = Path.GetFileName(selectedDllPath);
            AppendStatus($"Starting injection of {dllName} into {selectedProcess.ProcessName} (PID: {selectedProcess.ProcessId})");

            btnInject.Enabled = false;
            btnInject.Text = "🔄 INJECTING...";

            // Use async to prevent UI freezing
            Task.Run(() =>
            {
                bool success = InjectDLL(selectedProcess.ProcessId);

                // Update UI on main thread
                this.Invoke((Action)(() =>
                {
                    btnInject.Enabled = true; // Always re-enable for multiple injections

                    if (success)
                    {
                        btnInject.Text = "🚀 INJECT DLL";
                        AppendStatus($"🎉 INJECTION COMPLETE! {dllName} injected successfully.");
                        AppendStatus("💡 You can inject more DLLs or inject into other processes.");
                    }
                    else
                    {
                        btnInject.Text = "🚀 INJECT DLL";
                        AppendStatus("❌ INJECTION FAILED! Check the status messages above.");
                    }
                }));
            });
        }

        private void cmbDllSelection_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbDllSelection.SelectedIndex >= 0 && cmbDllSelection.SelectedIndex < availableDlls.Count)
            {
                selectedDllPath = availableDlls[cmbDllSelection.SelectedIndex];
                lblDllPath.Text = $"DLL: {Path.GetFileName(selectedDllPath)}";
                AppendStatus($"Selected DLL: {Path.GetFileName(selectedDllPath)}");
            }
        }

        private void btnBrowseDll_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "DLL files (*.dll)|*.dll|All files (*.*)|*.*";
                openFileDialog.Title = "Select DLL to inject";
                openFileDialog.InitialDirectory = Application.StartupPath;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    selectedDllPath = openFileDialog.FileName;
                    string fileName = Path.GetFileName(selectedDllPath);

                    // Add to combo box if not already there
                    bool found = false;
                    for (int i = 0; i < availableDlls.Count; i++)
                    {
                        if (availableDlls[i].Equals(selectedDllPath, StringComparison.OrdinalIgnoreCase))
                        {
                            cmbDllSelection.SelectedIndex = i;
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                    {
                        availableDlls.Add(selectedDllPath);
                        cmbDllSelection.Items.Add(fileName);
                        cmbDllSelection.SelectedIndex = cmbDllSelection.Items.Count - 1;
                    }

                    lblDllPath.Text = $"DLL: {fileName}";
                    AppendStatus($"Selected DLL: {fileName}");
                }
            }
        }
    }
}
