<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAMMOAADDDgAAAAAAAAAA
        AADPmLLDz5iy9c+YsnfPmLIEz5iyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAM+YsvPPmLL/z5iy8c+YsmvPmLIEz5iyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAz5iyd8+YsvDPmLL/z5iy8c+YsmzPmLIEz5iyAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIEz5iya8+YsvHPmLL/z5iy8c+Ysm/PmLIez5iyH8+Y
        sh/PmLIfz5iyHM+YshDPmLIDz5iyAM2YsgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM+YsgDPmLIEz5iybM+YsvHPmLL/z5iy9s+Y
        suHPmLLgz5iy4M+YsuDPmLLdz5iyzc+YsqLPmLJZz5iyE8+YsgDPmLIAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM+YsgDPmLIDz5iyb8+Y
        svfPmLL/z5iy/8+Ysv/PmLL/z5iy/8+Ysv/PmLL/z5iy/8+YsvrPmLLDz5iySc+YsgLPmLIAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM+Y
        sgDPmLIgz5iy4c+Ysv/PmLLfz5iypc+YsqXPmLKlz5iyqs+YssbPmLLvz5iy/8+Ysv/PmLLsz5iyac+Y
        sgTPmLIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAz5iyAM+Ysh/PmLLgz5iy/8+YsqXPl7ICz5eyAc+WsgHPmLICz5iyDc+YsjrPmLKgz5iy9s+Y
        sv/PmLLxz5iybM+YsgTPmLIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADPmLIAz5iyH8+YsuDPmLL/z5iypc+XsgHQnLIAz5iyAdCWsgDRlrIAz5iyAM+Y
        sgjPmLJwz5iy8c+Ysv/PmLLxz5iybM+YsgTPmLIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAM+YsgDPmLIfz5iy4M+Ysv/PmLKkz5iyBs+Ysl3PmLKbz5iyNM+Y
        sgDPmLIAz5iyAM+YsgTPmLJsz5iy8c+Ysv/PmLLxz5iybM+YsgTPmLIAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAz5iyAM+YshzPmLLdz5iy/8+Ysq7PmLJuz5iy8c+Y
        sv/PmLKZz5iyAM+YsgAAAAAAz5iyAM+YsgTPmLJsz5iy8c+Ysv/PmLLxz5iybM+YsgTPmLIAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iyEc+YsszPmLL/z5iy8M+Y
        svLPmLL/z5iy8s+Ysl3PmLIAz5iyAAAAAAAAAAAAz5iyAM+YsgTPmLJsz5iy8c+Ysv/PmLLxz5iybM+Y
        sgTPmLIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM+YsgDPmLICz5iyos+Y
        sv/PmLL/z5iy/8+YsvHPmLJsz5iyBM+YsgAAAAAAAAAAAAAAAAAAAAAAz5iyAM+YsgTPmLJsz5iy8c+Y
        sv/PmLLxz5iybM+YsgTPmLIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzpiyAM+Y
        sgDPmLJaz5iy+s+Ysv/PmLL5z5iycM+YsgPPmLIAz5iyBM+YslvPmLKSz5iyLM+YsgDPmLIAz5iyAM+Y
        sgTPmLJsz5iy8c+Ysv/PmLLxz5iybM+YsgTPmLIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAz5iyAM+YshTPmLLCz5iy/8+YsvrPmLJzz5iyA8+YsgPPmLJsz5iy8c+Ysv/PmLKQz5iyAM+Y
        sgAAAAAAz5iyAM+YsgTPmLJsz5iy8c+Ysv/PmLLxz5iya8+YsgPPmLIAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADPmLIAz5iyAM+YskrPmLLsz5iy/8+YsvLPmLJuz5iybc+YsvHPmLL/z5iy8s+Y
        slvPmLIAz5iyAAAAAAAAAAAAz5iyAM+YsgTPmLJsz5iy8c+Ysv/PmLLwz5iyVs+YsgDPmLIAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iyAs+YsmnPmLLxz5iy/8+YsvbPmLL2z5iy/8+Y
        svHPmLJsz5iyBM+YsgAAAAAAAAAAAAAAAAAAAAAAz5iyAM+YsgTPmLJtz5iy88+Ysv/PmLLNz5iyGc+Y
        sgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iyBM+YsmzPmLLxz5iy/8+Y
        sv/PmLL3z5iybs+YsgPPmLIAz5iyBM+YslvPmLKSz5iyLM+YsgDPmLIAz5iyAM+YsgTPmLKRz5iy/8+Y
        svvPmLJZz5iyAM+ZsgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iyBM+Y
        smzPmLLxz5iy/8+YsvfPmLJuz5iyA8+YsgPPmLJsz5iy8c+Ysv/PmLKQz5iyAM+YsgAAAAAAz5iyAM+Y
        sjrPmLLxz5iy/8+YsozPmLIAz5iyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AADPmLIAz5iyBM+YsmzPmLLxz5iy/8+YsvHPmLJuz5iybs+YsvHPmLL/z5iy8s+YslvPmLIAz5iyAAAA
        AADPmLIAz5iyIs+YsuPPmLL/z5iyntmYsgDPmLIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADPmLIAz5iyBM+YsmzPmLLxz5iy/8+YsvbPmLL2z5iy/8+YsvHPmLJsz5iyBM+Y
        sgAAAAAAAAAAAM+YsgDPmLI2z5iy78+Ysv/PmLKOz5iyAM+YsgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iyBM+YsmzPmLLyz5iy/8+Ysv/PmLL3z5iybs+Y
        sgPPmLIAAAAAAAAAAADPmLIAz5iyAs+YsofPmLL/z5iy/M+YslzPmLIAz5iyAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iyBM+YsmzPmLLyz5iy/8+Y
        svfPmLJvz5iyBM+YsgAAAAAAz5iyAM+YsgPPmLJgz5iy7s+Ysv/PmLLPz5iyG8+YsgDPmLIAz5iyEs+Y
        sknPmLIoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iyBM+Y
        smzPmLLyz5iy/8+YsvPPmLKWz5iyP8+YsibPmLI7z5iyjM+Ysu/PmLL/z5iy/8+YspvPmLIEz5iyAM+Y
        shXPmLKjz5iy+8+YstUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AADPmLIAz5iyBM+YsmzPmLLwz5iy/8+Ysv/PmLL0z5iy58+YsvLPmLL+z5iy/8+Ysv/PmLL/z5iy8c+Y
        smvPmLIYz5iypM+Ysv7PmLL/z5iy2wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADPmLIAz5iyBM+YslbPmLLOz5iy+8+Ysv/PmLL/z5iy/8+YsvzPmLLPz5iym8+Y
        su/PmLL/z5iy78+YssrPmLL8z5iy/8+YstLPmLI/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAzZu0AM+YshnPmLJZz5iyjc+Ysp/PmLKPz5iyXM+Y
        shvPmLIEz5iya8+YsvDPmLL/z5iy/8+Ysv/PmLLUz5iyOM+YsgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAM+YsgDPmLIbz5iyys+Ysv/PmLL/z5iy1M+YsjjPmLIAz5iyAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAM6YsgDPmLIAz5iyFc+YsqTPmLL+z5iy/8+YstTPmLI4z5iyAM+YsgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAz5iyAM+YshDPmLKjz5iy/s+Ysv/PmLLUz5iyOM+YsgDPmLIAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPmLIAz5iySc+YsvjPmLL/z5iy08+YsjjPmLIAz5iyAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM+YsgDPmLIoz5iy1c+YstzPmLJAz5iyAM+Y
        sgAAAAAAAAAAAAAAAAAAAAAAD////wf///8D////AAf//4AB///AAH//4AA//+AAH//guA//4BwH/+Ae
        A//gHwH/4B+A//AhwH/wAeA/+AHwP/gB+B/8Ahwf/gAeH/8AHh//gB4f/8A8H//gOBj/8AAQ//gAAP/8
        AAD//wAB////A////gf///wP///8H////D8=
</value>
  </data>
</root>