#include <Windows.h>

namespace Engine
{


    	namespace CGame_Character
	{
		static unsigned long *m_Master = (unsigned long*)0x00906824;
		static unsigned long (__cdecl *FindCharacter)(unsigned int nID, int) = (unsigned long (__cdecl*)(unsigned int, int))0x00428490;
	}
    	namespace KGameSys
	{
        		static void (__cdecl *AddChattingMessage)(char Type, const char* Message, int Color) = (void (__cdecl*)(char, const char*, int))0x006E72C0;
		        static void (__cdecl *AddInfoMessage)(const char* Message, int Color, int type) = (void (__cdecl*)(const char*, int, int))0x006E72F0;
        		static int (__cdecl *OpenWindow)(const char *name, int Argument, int Value, int Type, int nForce, int x) = (int (__cdecl*)(const char*,int,int,int,int,int))0x006DACC0;

    }
}