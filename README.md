# Heth AB - Engine Memory Reader

This project consists of a C++ DLL with Microsoft Detours for memory reading and a C# WinForms application for user interface.

## Project Structure

- **AB DLL/AB DLL/AB DLL/** - C++ DLL project with memory reading capabilities
- **Heth AB/Heth AB/** - C# WinForms application for user interface

## Features

- **Process Detection**: Automatically finds and lists all running `engine.exe` processes
- **DLL Injection**: Injects the C++ DLL into the selected engine process
- **Memory Reading**: Reads player X and Y coordinates from game memory
- **Real-time Updates**: Continuously displays updated coordinates in the UI
- **Shared Memory Communication**: Uses shared memory for efficient data transfer between DLL and C# app

## Prerequisites

1. **Microsoft Detours Library**: You need to install Microsoft Detours
   - Download from: https://github.com/Microsoft/Detours
   - Build and install the library
   - Ensure `detours.lib` is available in your library path

2. **Visual Studio 2022** with C++ and .NET Framework support

## Building the Project

### C++ DLL (AB DLL)

1. Open `AB DLL/AB DLL/AB DLL.sln` in Visual Studio
2. Ensure Microsoft Detours is properly installed and linked
3. Build the solution (Release or Debug configuration)
4. The output will be `AB DLL.dll`

### C# Application (Heth AB)

1. Open `Heth AB/Heth AB.sln` in Visual Studio
2. Build the solution
3. Copy the built `AB DLL.dll` to the same directory as the C# executable

## Usage

1. **Start the Application**: Run the Heth AB executable
2. **Find Processes**: Click "Refresh" to scan for `engine.exe` processes
3. **Select Process**: Choose the target process from the dropdown
4. **Inject DLL**: Click "Inject DLL" to inject the memory reader into the process
5. **View Coordinates**: The player X and Y coordinates will be displayed and updated in real-time

## Memory Addresses

The current implementation reads from these memory locations:
- **Master Pointer**: `0x00906824`
- **Player X**: `Master + 18236` (offset)
- **Player Y**: `Master + 18244` (offset)

To modify these addresses, edit the values in `MemoryReader.h`:

```cpp
namespace Engine
{
    namespace CGame_Character
    {
        static unsigned long* m_Master = (unsigned long*)0x00906824;
    }
}
```

And update the offsets in `MemoryReader.cpp`:

```cpp
int x = *(DWORD*)(masterValue + 18236);
int y = *(DWORD*)(masterValue + 18244);
```

## Architecture

### C++ DLL Components

- **MemoryReader.h/cpp**: Core memory reading functionality
- **dllmain.cpp**: DLL entry point with Detours initialization
- **Shared Memory**: Communication mechanism with C# application

### C# Application Components

- **Process Enumeration**: Finds target processes
- **DLL Injection**: Injects the C++ DLL using Win32 API
- **Shared Memory Client**: Reads data from the injected DLL
- **Real-time UI**: Updates coordinates display every 100ms

## Security Considerations

- The application requires administrator privileges for DLL injection
- Only inject into processes you own or have permission to modify
- The memory addresses are specific to the target application version

## Troubleshooting

1. **"DLL not found"**: Ensure `AB DLL.dll` is in the same directory as the C# executable
2. **"Failed to open target process"**: Run the application as administrator
3. **"No engine.exe processes found"**: Make sure the target application is running
4. **Coordinates show "N/A"**: The memory addresses may be incorrect for your target application version

## Extending the System

To add more memory reading capabilities:

1. Add new data structures to both C++ and C# projects
2. Update the shared memory structure in `MemoryReader.h`
3. Implement reading logic in `ReadPlayerData()` function
4. Update the C# UI to display the new data

## License

This project is for educational purposes. Ensure you comply with the terms of service of any applications you interact with.
