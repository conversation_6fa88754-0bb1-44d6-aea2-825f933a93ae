// dllmain.cpp : Defines the entry point for the DLL application.
#include "pch.h"
#include "MemoryReader.h"

// Ensure that the Detours library is linked
#pragma comment(lib,"Detours/detours.lib")
#include "Detours/detours.h"


bool mySkillCast() {

    return true;
}


BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{

    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // Initialize detours
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());
        // Add any function hooks here if needed
        DetourAttach(&(PVOID&)Engine::KGameSys::Chk_SkillCast, mySkillCast);

        DetourTransactionCommit();

        // Test message to confirm DLL injection works
        //Engine::KGameSys::AddChattingMessage('*', "[HethAB] DLL Loaded", 0xFF0000);

        // Load configuration
        LoadConfig();
        ShowConsole();
        // Initialize keyboard monitor
        InitializeKeyboardMonitor();
        break;

    case DLL_THREAD_ATTACH:
        break;

    case DLL_THREAD_DETACH:
        break;

    case DLL_PROCESS_DETACH:
        // Cleanup keyboard monitor and bot threads
        CleanupKeyboardMonitor();

        // Cleanup detours
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());
        // Remove any function hooks here if needed
        DetourTransactionCommit();
        break;
    }
    return TRUE;
}

